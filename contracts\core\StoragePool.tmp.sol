// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "../governance/GovernanceModule.sol";
import "../governance/libraries/ProposalTypes.sol";

interface IPool {
    function transferTokens(uint256 amount) external returns (bool);
    function receiveTokens(address from, uint256 amount) external returns (bool);
}

/// @title StoragePool
/// @notice Manages device pools, join requests, and token locking with governance controls
/// @dev Inherits governance functionality from GovernanceModule (UUPS, Ownable, Pausable, AccessControl)
contract StoragePool is Initializable, GovernanceModule {
    using SafeERC20 for IERC20;

    // Token to be staked for pool creation and joining (e.g., FULA token)
    IERC20 public storageToken;
    // Secure pool contract holding the staked tokens (separate token vault)
    address public tokenPool;

    uint256 public createPoolLockAmount;  // Global token amount required to create a new pool

    /// @notice Structure representing a pool of members/devices
    struct Pool {
        uint32 id;
        string name;
        string region;
        uint256 requiredTokens;                // Tokens required for a member to join this pool
        uint32 maxChallengeResponsePeriod;     // Max challenge response time (seconds) for devices
        address creator;
        uint256 minPingTime;                   // Minimum ping time requirement (in milliseconds)
        uint32 memberCount;                    // Number of peerIDs currently active in pool
        uint32 maxMembers;                     // Max peerIDs allowed in pool (including pending requests)
        address[] memberList;                  // Unique member accounts in the pool
        mapping(address => uint256) memberIndex;      // Index of member in memberList
        mapping(address => string[]) memberPeerIds;   // PeerIDs per member account
        mapping(string => address) peerIdToMember;    // Maps each peerID to its member account
        mapping(string => uint256) lockedTokens;      // Tokens locked for each peerID in the pool
    }

    /// @notice Structure for a pending join request
    struct JoinRequest {
        address account;         // Requester’s address
        uint32 poolId;
        uint32 timestamp;
        uint8 status;            // 0: pending, 1: approved, 2: rejected
        uint128 approvals;
        uint128 rejections;
        string peerId;
        mapping(string => bool) votes;   // Record of which peerIDs (members) have voted
        uint32 index;            // Index in joinRequestKeys list for removal
    }

    // Storage of pools and join requests
    mapping(uint32 => Pool) private pools;
    uint32[] public poolIds;
    mapping(uint32 => uint256) private poolIndex;  // Index of poolId in poolIds array

    mapping(uint32 => string[]) private joinRequestKeys;               // Active join request peerIds per pool
    mapping(uint32 => mapping(string => uint256)) private joinRequestIndex;  // Index of a peerId in joinRequestKeys
    mapping(uint32 => mapping(string => JoinRequest)) private joinRequests;  // Join request data per pool per peerId

    // Forfeit (ban) flag for users
    mapping(address => bool) public isForfeited;

    /// @notice Events
    event PoolCreated(uint32 indexed poolId, address indexed creator, string name, string region, uint256 requiredTokens, uint32 maxMembers);
    event JoinRequestSubmitted(uint32 indexed poolId, address indexed account, string peerId);
    event JoinRequestResolved(uint32 indexed poolId, address indexed account, string peerId, bool approved, bool tokensForfeited);
    event MemberAdded(uint32 indexed poolId, address indexed account, string peerId, address indexed addedBy);
    event MemberRemoved(uint32 indexed poolId, address indexed account, string peerId, bool tokensForfeited, address removedBy);
    event ForfeitFlagSet(address indexed account);
    event ForfeitFlagCleared(address indexed account);
    event PoolParametersUpdated(uint32 indexed poolId, uint256 requiredTokens, uint32 maxMembers);
    event EmergencyTokensRecovered(uint256 amount);

    /// @notice Custom Errors for gas-efficient reverts
    error PoolNotFound(uint32 poolId);
    error AlreadyInPool(string peerId);
    error AlreadyRequested(string peerId);
    error PeerNotFound(string peerId);
    error CapacityReached(uint32 poolId);
    error UserFlagged(address user);
    error NotMember(address user);
    error AlreadyVoted(string voterPeerId);
    error NoActiveRequest(string peerId);
    error OnlyCreatorOrAdmin();
    error PoolNotEmpty(uint32 poolId);
    error PendingRequestsExist(uint32 poolId);
    error InvalidTokenAmount();
    error InsufficientAllowance(uint256 required);

    /// @notice Initialize the StoragePool contract
    /// @param _storageToken Address of the ERC20 token used for staking
    /// @param _tokenPool Address of the token-holding pool contract (for locked tokens)
    /// @param initialOwner Initial owner address for governance
    /// @param initialAdmin Initial admin address for governance
    function initialize(
        address _storageToken,
        address _tokenPool,
        address initialOwner,
        address initialAdmin
    ) public reinitializer(1) {
        if (_storageToken == address(0) || _tokenPool == address(0) || initialOwner == address(0) || initialAdmin == address(0)) {
            revert InvalidAddress();
        }
        __GovernanceModule_init(initialOwner, initialAdmin);
        storageToken = IERC20(_storageToken);
        tokenPool = _tokenPool;
    }

    /// @notice Internal function to remove a peer from a pool and handle token unlock/forfeit
    /// @dev Assumes caller has checked authorization and pool existence
    /// @param poolId The pool identifier
    /// @param peerId The peerId to remove
    function _removePeerFromPool(uint32 poolId, string memory peerId) internal {
        Pool storage pool = pools[poolId];
        address account = pool.peerIdToMember[peerId];
        uint256 amount = pool.lockedTokens[peerId];
        bool forfeited = isForfeited[account];
        // Remove peer from mappings
        delete pool.peerIdToMember[peerId];
        delete pool.lockedTokens[peerId];
        // Remove peerId from the member's list of peerIds
        string[] storage peerArray = pool.memberPeerIds[account];
        for (uint256 i = 0; i < peerArray.length; i++) {
            if (keccak256(bytes(peerArray[i])) == keccak256(bytes(peerId))) {
                // Swap with last and pop
                peerArray[i] = peerArray[peerArray.length - 1];
                peerArray.pop();
                break;
            }
        }
        // If member has no remaining peerIDs, remove member from pool completely
        if (peerArray.length == 0) {
            uint256 idx = pool.memberIndex[account];
            address lastAddr = pool.memberList[pool.memberList.length - 1];
            pool.memberList[idx] = lastAddr;
            pool.memberIndex[lastAddr] = idx;
            pool.memberList.pop();
            delete pool.memberIndex[account];
        }
        // Decrement active member count
        if (pool.memberCount > 0) {
            pool.memberCount -= 1;
        }
        // Handle token return or confiscation
        if (amount > 0) {
            IPool(tokenPool).transferTokens(amount);  // retrieve tokens from pool
            if (forfeited) {
                // Send forfeited tokens to the StorageToken contract address
                storageToken.safeTransfer(address(storageToken), amount);
            } else {
                // Refund tokens to the user
                storageToken.safeTransfer(account, amount);
            }
        }
        emit MemberRemoved(poolId, account, peerId, forfeited, msg.sender);
    }

    /// @notice Create a new pool (device group)
    /// @param name Name of the pool
    /// @param region Geographic or category description of the pool
    /// @param requiredTokens Token amount required for joining this pool (must be ≤ global createPoolLockAmount)
    /// @param maxChallengeResponsePeriod Max challenge response time for devices (seconds)
    /// @param minPingTime Minimum ping time requirement
    /// @param maxMembers Maximum number of members (peerIDs) allowed (0 = no limit)
    /// @param peerId Optional peerId of creator's device to immediately join the pool
    function createPool(
        string memory name,
        string memory region,
        uint256 requiredTokens,
        uint32 maxChallengeResponsePeriod,
        uint256 minPingTime,
        uint32 maxMembers,
        string memory peerId
    )
        external
        whenNotPaused
        nonReentrant
    {
        bool isPrivileged = hasRole(ProposalTypes.ADMIN_ROLE, msg.sender) || hasRole(ProposalTypes.POOL_ADMIN_ROLE, msg.sender);
        if (isForfeited[msg.sender]) revert UserFlagged(msg.sender);
        if (!isPrivileged) {
            if (bytes(peerId).length == 0) revert InvalidAddress();
            // Non-admin users must lock tokens to create the pool
            if (createPoolLockAmount > 0) {
                if (storageToken.allowance(msg.sender, address(this)) < createPoolLockAmount) {
                    revert InsufficientAllowance(createPoolLockAmount);
                }
                storageToken.safeTransferFrom(msg.sender, tokenPool, createPoolLockAmount);
                IPool(tokenPool).receiveTokens(msg.sender, createPoolLockAmount);
            }
        }
        // Validate requiredTokens cap
        if (requiredTokens > createPoolLockAmount) {
            requiredTokens = createPoolLockAmount;
        }
        // Generate new pool ID
        uint32 poolId = uint32(poolIds.length + 1);
        Pool storage pool = pools[poolId];
        pool.id = poolId;
        pool.name = name;
        pool.region = region;
        pool.requiredTokens = requiredTokens;
        pool.maxChallengeResponsePeriod = maxChallengeResponsePeriod;
        pool.creator = msg.sender;
        pool.minPingTime = minPingTime;
        pool.memberCount = 0;
        pool.maxMembers = maxMembers;
        // Register pool
        poolIndex[poolId] = poolIds.length;
        poolIds.push(poolId);
        // If creator provided a peerId, add creator as initial member
        if (bytes(peerId).length > 0) {
            pool.peerIdToMember[peerId] = msg.sender;
            pool.memberPeerIds[msg.sender].push(peerId);
            pool.memberList.push(msg.sender);
            pool.memberIndex[msg.sender] = pool.memberList.length - 1;
            pool.memberCount = 1;
            // Lock the creator's tokens to this peerId (if any were required)
            uint256 lockedAmt = 0;
            if (!isPrivileged) {
                lockedAmt = createPoolLockAmount;
            }
            pool.lockedTokens[peerId] = lockedAmt;
        }
        emit PoolCreated(poolId, msg.sender, name, region, requiredTokens, maxMembers);
    }

    /// @notice Submit a join request to a pool with a given peerId (locks required tokens)
    /// @param poolId The target pool ID
    /// @param peerId The peerId of the requesting device
    function joinPoolRequest(uint32 poolId, string memory peerId)
        external
        whenNotPaused
        nonReentrant
    {
        Pool storage pool = pools[poolId];
        if (pool.id != poolId) revert PoolNotFound(poolId);
        if (isForfeited[msg.sender]) revert UserFlagged(msg.sender);
        if (pool.peerIdToMember[peerId] != address(0)) revert AlreadyInPool(peerId);
        if (joinRequests[poolId][peerId].account != address(0) && joinRequests[poolId][peerId].status == 0) {
            revert AlreadyRequested(peerId);
        }
        // Enforce maxMembers limit (count active members + pending requests)
        if (pool.maxMembers != 0 && (pool.memberCount + joinRequestKeys[poolId].length) >= pool.maxMembers) {
            revert CapacityReached(poolId);
        }
        uint256 reqAmount = pool.requiredTokens;
        if (reqAmount > 0) {
            if (storageToken.allowance(msg.sender, address(this)) < reqAmount) {
                revert InsufficientAllowance(reqAmount);
            }
            storageToken.safeTransferFrom(msg.sender, tokenPool, reqAmount);
            IPool(tokenPool).receiveTokens(msg.sender, reqAmount);
        }
        // Create join request entry
        JoinRequest storage req = joinRequests[poolId][peerId];
        req.account = msg.sender;
        req.poolId = poolId;
        req.timestamp = uint32(block.timestamp);
        req.status = 0;
        req.approvals = 0;
        req.rejections = 0;
        req.peerId = peerId;
        // Track this request in the pending list
        joinRequestIndex[poolId][peerId] = joinRequestKeys[poolId].length;
        joinRequestKeys[poolId].push(peerId);
        req.index = uint32(joinRequestKeys[poolId].length - 1);
        emit JoinRequestSubmitted(poolId, msg.sender, peerId);
    }

    /// @notice Cast a vote on a pending join request (must be a current member of the pool)
    /// @param poolId The pool ID
    /// @param peerId The peerId of the pending join request
    /// @param voterPeerId The voter's own peerId in the pool
    /// @param approve True to vote approve, false to vote reject
    function voteOnJoinRequest(uint32 poolId, string memory peerId, string memory voterPeerId, bool approve)
        external
        whenNotPaused
    {
        JoinRequest storage req = joinRequests[poolId][peerId];
        if (req.account == address(0) || req.status != 0) revert NoActiveRequest(peerId);
        Pool storage pool = pools[poolId];
        // Verify that the caller is the owner of the voterPeerId in this pool
        address voterAccount = pool.peerIdToMember[voterPeerId];
        if (voterAccount == address(0) || voterAccount != msg.sender) revert NotMember(msg.sender);
        if (req.votes[voterPeerId]) revert AlreadyVoted(voterPeerId);
        // Record the vote
        req.votes[voterPeerId] = true;
        if (approve) {
            req.approvals += 1;
        } else {
            req.rejections += 1;
        }
        // Calculate current approval threshold
        uint256 memberCount = pool.memberCount;
        uint256 threshold;
        if (memberCount <= 2) {
            threshold = 1;
        } else {
            threshold = (memberCount + 2) / 3;
        }
        if (threshold > 10) {
            threshold = 10;
        }
        bool forfeited = isForfeited[req.account];
        // Check if request can be finalized
        if (req.approvals >= threshold && !forfeited) {
            // Approve and add the new member
            req.status = 1;
            pool.peerIdToMember[peerId] = req.account;
            pool.memberPeerIds[req.account].push(peerId);
            if (pool.memberPeerIds[req.account].length == 1) {
                pool.memberList.push(req.account);
                pool.memberIndex[req.account] = pool.memberList.length - 1;
            }
            pool.memberCount += 1;
            pool.lockedTokens[peerId] = pool.requiredTokens;
            // Remove request from pending list
            uint256 idx = joinRequestIndex[poolId][peerId];
            string memory lastKey = joinRequestKeys[poolId][joinRequestKeys[poolId].length - 1];
            joinRequestKeys[poolId][idx] = lastKey;
            joinRequestIndex[poolId][lastKey] = idx;
            joinRequestKeys[poolId].pop();
            delete joinRequestIndex[poolId][peerId];
            emit JoinRequestResolved(poolId, req.account, peerId, true, false);
            delete joinRequests[poolId][peerId];
        } else if (req.rejections >= threshold || forfeited) {
            // Reject the request
            req.status = 2;
            uint256 idx = joinRequestIndex[poolId][peerId];
            string memory lastKey = joinRequestKeys[poolId][joinRequestKeys[poolId].length - 1];
            joinRequestKeys[poolId][idx] = lastKey;
            joinRequestIndex[poolId][lastKey] = idx;
            joinRequestKeys[poolId].pop();
            delete joinRequestIndex[poolId][peerId];
            // Refund or forfeit locked tokens
            uint256 amount = pool.requiredTokens;
            if (amount > 0) {
                IPool(tokenPool).transferTokens(amount);
                if (forfeited) {
                    storageToken.safeTransfer(address(storageToken), amount);
                } else {
                    storageToken.safeTransfer(req.account, amount);
                }
            }
            emit JoinRequestResolved(poolId, req.account, peerId, false, forfeited);
            delete joinRequests[poolId][peerId];
        }
    }

    /// @notice Cancel a pending join request (by the requester, pool creator, or an admin)
    /// @param poolId The pool ID
    /// @param peerId The peerId of the join request to cancel
    function cancelJoinRequest(uint32 poolId, string memory peerId)
        external
        whenNotPaused
        nonReentrant
    {
        JoinRequest storage req = joinRequests[poolId][peerId];
        if (req.account == address(0) || req.status != 0) revert NoActiveRequest(peerId);
        Pool storage pool = pools[poolId];
        bool isPrivileged = hasRole(ProposalTypes.ADMIN_ROLE, msg.sender) || hasRole(ProposalTypes.POOL_ADMIN_ROLE, msg.sender);
        if (!(msg.sender == req.account || isPrivileged || msg.sender == pool.creator)) {
            revert OnlyCreatorOrAdmin();
        }
        // Mark as rejected
        req.status = 2;
        // Remove from pending list
        uint256 idx = joinRequestIndex[poolId][peerId];
        string memory lastKey = joinRequestKeys[poolId][joinRequestKeys[poolId].length - 1];
        joinRequestKeys[poolId][idx] = lastKey;
        joinRequestIndex[poolId][lastKey] = idx;
        joinRequestKeys[poolId].pop();
        delete joinRequestIndex[poolId][peerId];
        // Refund or forfeit tokens
        uint256 amount = pool.requiredTokens;
        bool forfeited = isForfeited[req.account];
        if (amount > 0) {
            IPool(tokenPool).transferTokens(amount);
            if (forfeited) {
                storageToken.safeTransfer(address(storageToken), amount);
            } else {
                storageToken.safeTransfer(req.account, amount);
            }
        }
        emit JoinRequestResolved(poolId, req.account, peerId, false, forfeited);
        delete joinRequests[poolId][peerId];
    }

    /// @notice Approve a pending join request immediately (pool admin override)
    /// @param poolId The pool ID
    /// @param peerId The peerId of the join request to approve
    function approveJoinRequest(uint32 poolId, string memory peerId)
        external
        whenNotPaused
        nonReentrant
        onlyRole(ProposalTypes.POOL_ADMIN_ROLE)
    {
        JoinRequest storage req = joinRequests[poolId][peerId];
        if (req.account == address(0) || req.status != 0) revert NoActiveRequest(peerId);
        if (isForfeited[req.account]) revert UserFlagged(req.account);
        Pool storage pool = pools[poolId];
        // Enforce capacity (pool admin should not exceed maxMembers)
        if (pool.maxMembers != 0 && (pool.memberCount + 1) > pool.maxMembers) {
            revert CapacityReached(poolId);
        }
        // Approve and add member to pool
        req.status = 1;
        pool.peerIdToMember[peerId] = req.account;
        pool.memberPeerIds[req.account].push(peerId);
        if (pool.memberPeerIds[req.account].length == 1) {
            pool.memberList.push(req.account);
            pool.memberIndex[req.account] = pool.memberList.length - 1;
        }
        pool.memberCount += 1;
        pool.lockedTokens[peerId] = pool.requiredTokens;
        // Remove from pending list
        uint256 idx = joinRequestIndex[poolId][peerId];
        string memory lastKey = joinRequestKeys[poolId][joinRequestKeys[poolId].length - 1];
        joinRequestKeys[poolId][idx] = lastKey;
        joinRequestIndex[poolId][lastKey] = idx;
        joinRequestKeys[poolId].pop();
        delete joinRequestIndex[poolId][peerId];
        emit JoinRequestResolved(poolId, req.account, peerId, true, false);
        delete joinRequests[poolId][peerId];
    }

    /// @notice Add a member peerId to a pool without a join request (admin only)
    /// @param poolId The pool ID
    /// @param account The address of the member to add
    /// @param peerId The peerId of the device to add for that member
    function addMember(uint32 poolId, address account, string memory peerId)
        external
        whenNotPaused
        onlyRole(ProposalTypes.POOL_ADMIN_ROLE)
    {
        Pool storage pool = pools[poolId];
        if (pool.id != poolId) revert PoolNotFound(poolId);
        if (isForfeited[account]) revert UserFlagged(account);
        if (pool.peerIdToMember[peerId] != address(0)) revert AlreadyInPool(peerId);
        if (joinRequests[poolId][peerId].account != address(0) && joinRequests[poolId][peerId].status == 0) {
            revert AlreadyRequested(peerId);
        }
        if (pool.maxMembers != 0 && (pool.memberCount + joinRequestKeys[poolId].length) >= pool.maxMembers) {
            revert CapacityReached(poolId);
        }
        // Directly add member without requiring any token stake
        pool.peerIdToMember[peerId] = account;
        pool.memberPeerIds[account].push(peerId);
        if (pool.memberPeerIds[account].length == 1) {
            pool.memberList.push(account);
            pool.memberIndex[account] = pool.memberList.length - 1;
        }
        pool.memberCount += 1;
        pool.lockedTokens[peerId] = 0;
        emit MemberAdded(poolId, account, peerId, msg.sender);
    }

    /// @notice Remove a member's peerId from a pool (member themselves, pool creator, or admin)
    /// @param poolId The pool ID
    /// @param peerId The peerId to remove
    function removeMemberPeerId(uint32 poolId, string memory peerId)
        external
        whenNotPaused
        nonReentrant
    {
        Pool storage pool = pools[poolId];
        if (pool.id != poolId) revert PoolNotFound(poolId);
        address memberAccount = pool.peerIdToMember[peerId];
        if (memberAccount == address(0)) revert PeerNotFound(peerId);
        bool isPrivileged = hasRole(ProposalTypes.ADMIN_ROLE, msg.sender) || hasRole(ProposalTypes.POOL_ADMIN_ROLE, msg.sender);
        if (!(msg.sender == memberAccount || isPrivileged || msg.sender == pool.creator)) {
            revert OnlyCreatorOrAdmin();
        }
        _removePeerFromPool(poolId, peerId);
    }

    /// @notice Batch remove members from a pool, up to a provided count (to avoid gas limits)
    /// @param poolId The pool ID
    /// @param count The maximum number of member accounts to remove
    function removeMembersBatch(uint32 poolId, uint256 count)
        external
        whenNotPaused
        nonReentrant
    {
        Pool storage pool = pools[poolId];
        if (pool.id != poolId) revert PoolNotFound(poolId);
        bool isPrivileged = hasRole(ProposalTypes.ADMIN_ROLE, msg.sender) || hasRole(ProposalTypes.POOL_ADMIN_ROLE, msg.sender);
        if (!(isPrivileged || msg.sender == pool.creator)) {
            revert OnlyCreatorOrAdmin();
        }
        uint256 removed = 0;
        bool keepCreator = (msg.sender == pool.creator);
        while (removed < count) {
            uint256 mCount = pool.memberList.length;
            if (mCount == 0) break;
            if (keepCreator && mCount == 1) break;
            // Remove the last member in the list (and all their peerIds)
            address target = pool.memberList[mCount - 1];
            string[] memory peers = pool.memberPeerIds[target];
            uint256 peerCount = peers.length;
            for (uint256 i = 0; i < peerCount; i++) {
                string memory lastPeer = peers[peerCount - 1 - i];
                _removePeerFromPool(poolId, lastPeer);
            }
            removed += 1;
        }
    }

    /// @notice Delete a pool (only if empty or only creator remains)
    /// @param poolId The pool ID to delete
    function deletePool(uint32 poolId)
        external
        whenNotPaused
        nonReentrant
    {
        Pool storage pool = pools[poolId];
        if (pool.id != poolId) revert PoolNotFound(poolId);
        if (!(msg.sender == pool.creator || hasRole(ProposalTypes.ADMIN_ROLE, msg.sender))) {
            revert OnlyCreatorOrAdmin();
        }
        uint256 memberCount = pool.memberCount;
        if (msg.sender == pool.creator) {
            // Creator can delete only if they are the only member (or no members)
            if (!(memberCount == 0 || (memberCount == 1 && pool.memberList.length == 1 && pool.memberList[0] == msg.sender))) {
                revert PoolNotEmpty(poolId);
            }
        } else {
            // Admin must ensure pool is empty (no members remain)
            if (memberCount != 0) revert PoolNotEmpty(poolId);
        }
        // If creator is still a member, remove their peer(s) and unlock tokens
        if (pool.memberList.length > 0) {
            address target = pool.memberList[0];
            string[] memory peers = pool.memberPeerIds[target];
            for (uint256 i = 0; i < peers.length; i++) {
                _removePeerFromPool(poolId, peers[i]);
            }
        }
        // Remove pool from registry
        uint256 idx = poolIndex[poolId];
        uint32 lastPoolId = poolIds[poolIds.length - 1];
        poolIds[idx] = lastPoolId;
        poolIndex[lastPoolId] = idx;
        poolIds.pop();
        delete poolIndex[poolId];
        delete pools[poolId];
    }

    /// @notice Set the maximum number of members allowed in a pool (admin/pool admin)
    function setMaxMembers(uint32 poolId, uint32 newMax)
        external
        whenNotPaused
        onlyRole(ProposalTypes.POOL_ADMIN_ROLE)
    {
        Pool storage pool = pools[poolId];
        if (pool.id != poolId) revert PoolNotFound(poolId);
        // Prevent lowering below current total usage (members + pending requests)
        uint256 currentUsage = pool.memberCount + joinRequestKeys[poolId].length;
        if (newMax != 0 && newMax < currentUsage) {
            revert PendingRequestsExist(poolId);
        }
        pool.maxMembers = newMax;
        emit PoolParametersUpdated(poolId, pool.requiredTokens, newMax);
    }

    /// @notice Update the token requirement for joining a pool (admin/pool admin)
    function setRequiredTokens(uint32 poolId, uint256 newRequired)
        external
        whenNotPaused
        onlyRole(ProposalTypes.POOL_ADMIN_ROLE)
    {
        Pool storage pool = pools[poolId];
        if (pool.id != poolId) revert PoolNotFound(poolId);
        if (newRequired > createPoolLockAmount) {
            newRequired = createPoolLockAmount;
        }
        // If raising requirement, ensure no active pending requests (to avoid inconsistency)
        if (newRequired > pool.requiredTokens && joinRequestKeys[poolId].length > 0) {
            revert PendingRequestsExist(poolId);
        }
        pool.requiredTokens = newRequired;
        emit PoolParametersUpdated(poolId, newRequired, pool.maxMembers);
    }

    /// @notice Set or clear the forfeit (ban) flag on a user (admin/pool admin)
    /// @param account The user address to flag or unflag
    /// @param flag True to set forfeit flag (ban user), False to clear it
    function setForfeitFlag(address account, bool flag)
        external
        whenNotPaused
        onlyRole(ProposalTypes.POOL_ADMIN_ROLE)
    {
        if (account == address(0)) revert InvalidAddress();
        isForfeited[account] = flag;
        if (flag) {
            emit ForfeitFlagSet(account);
        } else {
            emit ForfeitFlagCleared(account);
        }
    }

    /// @notice Emergency function to recover all tokens from the token pool to the StorageToken contract
    /// @param amount The amount of tokens to recover (if 0, function will revert)
    function emergencyRecoverTokens(uint256 amount)
        external
        nonReentrant
        onlyRole(ProposalTypes.ADMIN_ROLE)
    {
        if (amount == 0) revert InvalidTokenAmount();
        uint256 poolBalance = storageToken.balanceOf(tokenPool);
        if (amount > poolBalance) {
            amount = poolBalance;
        }
        if (amount == 0) return;
        IPool(tokenPool).transferTokens(amount);
        storageToken.safeTransfer(address(storageToken), amount);
        emit EmergencyTokensRecovered(amount);
    }

    // *** View Functions for querying contract state ***

    /// @notice Get basic information about a pool
    function getPool(uint32 poolId) external view returns (
        string memory name,
        string memory region,
        address creator,
        uint256 requiredTokens,
        uint32 memberCount,
        uint32 maxMembers,
        uint32 maxChallengeResponsePeriod,
        uint256 minPingTime
    ) {
        Pool storage pool = pools[poolId];
        name = pool.name;
        region = pool.region;
        creator = pool.creator;
        requiredTokens = pool.requiredTokens;
        memberCount = pool.memberCount;
        maxMembers = pool.maxMembers;
        maxChallengeResponsePeriod = pool.maxChallengeResponsePeriod;
        minPingTime = pool.minPingTime;
    }

    /// @notice Get all pending join requests for a pool
    function getPendingJoinRequests(uint32 poolId)
        external
        view
        returns (address[] memory accounts, string[] memory peerIds, uint128[] memory approvals, uint128[] memory rejections)
    {
        string[] storage keys = joinRequestKeys[poolId];
        uint256 len = keys.length;
        accounts = new address[](len);
        peerIds = new string[](len);
        approvals = new uint128[](len);
        rejections = new uint128[](len);
        for (uint256 i = 0; i < len; i++) {
            string storage pId = keys[i];
            JoinRequest storage req = joinRequests[poolId][pId];
            accounts[i] = req.account;
            peerIds[i] = pId;
            approvals[i] = req.approvals;
            rejections[i] = req.rejections;
        }
    }

    /// @notice Check if a given peerId is an active member of a pool
    function isPeerInPool(uint32 poolId, string memory peerId) external view returns (bool) {
        return pools[poolId].peerIdToMember[peerId] != address(0);
    }

    /// @notice Check if a given peerId currently has a pending join request in a pool
    function isJoinRequestPending(uint32 poolId, string memory peerId) external view returns (bool) {
        JoinRequest storage req = joinRequests[poolId][peerId];
        return (req.account != address(0) && req.status == 0);
    }

    /// @notice Get the amount of tokens locked for a specific peerId in a pool
    function getLockedTokens(uint32 poolId, string memory peerId) external view returns (uint256) {
        Pool storage pool = pools[poolId];
        if (pool.peerIdToMember[peerId] != address(0)) {
            return pool.lockedTokens[peerId];
        }
        // If peerId has a pending request, return the pool's requiredTokens (amount locked)
        JoinRequest storage req = joinRequests[poolId][peerId];
        if (req.account != address(0) && req.status == 0) {
            return pool.requiredTokens;
        }
        return 0;
    }

    /// @notice Authorize contract upgrades through governance (UUPS pattern)
    function _authorizeUpgrade(address newImplementation)
        internal
        nonReentrant
        whenNotPaused
        onlyRole(ProposalTypes.ADMIN_ROLE)
        override
    {
        if (!_checkUpgrade(newImplementation)) revert("UpgradeNotAuthorized");
    }

    /// @dev No custom governance proposal types for this contract
    function _createCustomProposal(
        uint8 proposalType,
        uint40,
        address,
        bytes32,
        uint96,
        address
    ) internal virtual override returns (bytes32) {
        revert InvalidProposalType(proposalType);
    }
    function _executeCustomProposal(bytes32 proposalId) internal virtual override {
        ProposalTypes.UnifiedProposal storage proposal = proposals[proposalId];
        revert InvalidProposalType(uint8(proposal.proposalType));
    }
}
